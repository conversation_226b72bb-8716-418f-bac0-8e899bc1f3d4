import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import Image from "next/image";

export default function Page() {
  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm">
        <div className="flex flex-col gap-6">
          <Card>
            <CardHeader className="text-center">
              {/* Success Icon */}
              <div className="mx-auto mb-4 flex h-20 w-20 items-center justify-center">
                <Image
                  src="/sign-up-success-icon.png"
                  alt="Success"
                  width={80}
                  height={80}
                  className="h-full w-full object-contain"
                  priority
                />
              </div>
              <CardTitle className="text-2xl">
                Cảm ơn bạn đã đăng ký tài khoản PawHub.
              </CardTitle>
            </CardHeader>
            <CardContent className="flex flex-col gap-4">
              <p className="text-sm text-muted-foreground text-center">
                Vui lòng kiểm tra email bạn sử dụng đăng ký và bấm vào link xác nhận để hoàn tất việc tạo tài khoản.
              </p>
              <Button asChild className="w-full">
                <Link href="/">Go to Home Page</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
