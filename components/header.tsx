"use client";

import Link from "next/link";
import { Client<PERSON>uthButton } from "@/components/client-auth-button";
import { ChevronDown } from "lucide-react";
import Image from "next/image";

export function Header() {
  return (
    <header className="w-full bg-white border-b border-gray-200 h-32">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full">
        <div className="flex justify-between items-center h-full">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2">
              <Image src="/paw-hub-logo.png" alt="Paw Hub Logo" width={93} height={57} />
              <span className="sr-only">
                Paw Hub
              </span>
            </Link>
          </div>

          {/* Navigation Menu */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link
              href="/"
              className="text-gray-900 font-semibold hover:text-blue-600 transition-colors"
            >
              Home
            </Link>
            <Link
              href="/ai-doctor"
              className="text-gray-700 hover:text-blue-600 transition-colors"
            >
              AI Doctor
            </Link>
            <div className="relative group">
              <Link
                href="/shop"
                className="text-gray-700 hover:text-blue-600 transition-colors flex items-center"
              >
                Shop
                <ChevronDown className="ml-1 h-4 w-4" />
              </Link>
            </div>
            <div className="relative group">
              <Link
                href="/pet-services"
                className="text-gray-700 hover:text-blue-600 transition-colors flex items-center"
              >
                Pet Services
                <ChevronDown className="ml-1 h-4 w-4" />
              </Link>
            </div>
            <div className="relative group">
              <Link
                href="/news"
                className="text-gray-700 hover:text-blue-600 transition-colors flex items-center"
              >
                News
                <ChevronDown className="ml-1 h-4 w-4" />
              </Link>
            </div>
          </nav>

          {/* Auth Section */}
          <ClientAuthButton />
        </div>
      </div>
    </header>
  );
}
